/**
 * Cookie 工具函数
 * 用于处理浏览器 cookie 的读取、设置和删除
 */

/**
 * 获取指定名称的 cookie 值
 * @param {string} name - cookie 名称
 * @returns {string|null} - cookie 值，如果不存在则返回 null
 */
export function getCookie(name) {
  if (typeof document === "undefined") {
    return null;
  }

  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);

  if (parts.length === 2) {
    const cookieValue = parts.pop().split(";").shift();
    return decodeURIComponent(cookieValue);
  }

  return null;
}

/**
 * 设置 cookie
 * @param {string} name - cookie 名称
 * @param {string} value - cookie 值
 * @param {Object} options - cookie 选项
 * @param {number} options.days - 过期天数
 * @param {string} options.path - 路径
 * @param {string} options.domain - 域名
 * @param {boolean} options.secure - 是否仅在 HTTPS 下传输
 * @param {string} options.sameSite - SameSite 属性
 */
export function setCookie(name, value, options = {}) {
  if (typeof document === "undefined") {
    return;
  }

  const { days = 7, path = "/", domain = "", secure = false, sameSite = "Lax" } = options;

  let cookieString = `${name}=${encodeURIComponent(value)}`;

  if (days) {
    const date = new Date();
    date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
    cookieString += `; expires=${date.toUTCString()}`;
  }

  cookieString += `; path=${path}`;

  if (domain) {
    cookieString += `; domain=${domain}`;
  }

  if (secure) {
    cookieString += "; secure";
  }

  cookieString += `; SameSite=${sameSite}`;

  document.cookie = cookieString;
}

/**
 * 删除指定名称的 cookie
 * @param {string} name - cookie 名称
 * @param {string} path - 路径
 * @param {string} domain - 域名
 */
export function removeCookie(name, path = "/", domain = "") {
  if (typeof document === "undefined") {
    return;
  }

  let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}`;

  if (domain) {
    cookieString += `; domain=${domain}`;
  }

  document.cookie = cookieString;
}

/**
 * 检查指定名称的 cookie 是否存在且有效
 * @param {string} name - cookie 名称
 * @returns {boolean} - 如果 cookie 存在且不为空则返回 true
 */
export function isCookieValid(name) {
  const value = getCookie(name);
  return value !== null && value !== "" && value !== "undefined";
}

/**
 * 获取所有 cookie
 * @returns {Object} - 包含所有 cookie 的对象
 */
export function getAllCookies() {
  if (typeof document === "undefined") {
    return {};
  }

  const cookies = {};
  const cookieArray = document.cookie.split(";");

  cookieArray.forEach((cookie) => {
    const [name, value] = cookie.trim().split("=");
    if (name && value) {
      cookies[name] = decodeURIComponent(value);
    }
  });

  return cookies;
}

/**
 * 清除所有 cookie
 * @param {string} path - 路径
 * @param {string} domain - 域名
 */
export function clearAllCookies(path = "/", domain = "") {
  if (typeof document === "undefined") {
    return;
  }

  const cookies = getAllCookies();

  Object.keys(cookies).forEach((name) => {
    removeCookie(name, path, domain);
  });
}

/**
 * 检查身份验证相关的 cookie 是否有效
 * 根据项目实际使用的 cookie 名称进行检查
 * @returns {boolean} - 如果身份验证 cookie 有效则返回 true
 */
export function isAuthCookieValid() {
  // 常见的身份验证 cookie 名称
  const authCookieNames = ["jeesite.session.id"];

  // 检查是否存在任何有效的身份验证 cookie
  return authCookieNames.some((name) => isCookieValid(name));
}

/**
 * 清除身份验证相关的 cookie
 * @param {string} path - 路径
 * @param {string} domain - 域名
 */
export function clearAuthCookies(path = "/", domain = "") {
  const authCookieNames = [
    "JSESSIONID",
    "sessionid",
    "auth_token",
    "access_token",
    "token",
    "user_token",
    "login_token",
    "session_token",
  ];

  authCookieNames.forEach((name) => {
    removeCookie(name, path, domain);
  });
}
