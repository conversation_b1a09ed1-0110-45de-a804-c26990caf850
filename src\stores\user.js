import { defineStore } from "pinia";
import { ref } from "vue";
import apiService from "@/utils/api";

export const useUserStore = defineStore(
  "user",
  () => {
    // 用户信息状态
    const userInfo = ref(null);
    // 用户个人资料状态
    const userProfile = ref(null);
    // 加载状态
    const loading = ref(false);

    // 从本地存储初始化用户信息
    const initUserInfo = () => {
      // 插件会自动从存储中恢复状态
    };

    // 设置用户信息
    const setUserInfo = (info) => {
      userInfo.value = info;
    };

    // 清除用户信息
    const clearUserInfo = () => {
      userInfo.value = null;
      userProfile.value = null;

      // 清除本地存储中的用户相关数据
      try {
        localStorage.removeItem('userInfo');
        localStorage.removeItem('erpsparts-user');
        sessionStorage.removeItem('userInfo');
      } catch (error) {
        console.warn('清除本地存储失败:', error);
      }
    };

    // 判断用户是否已登录
    const isLoggedIn = () => {
      return !!userInfo.value;
    };

    return {
      userInfo,
      userProfile,
      loading,
      initUserInfo,
      setUserInfo,
      clearUserInfo,
      isLoggedIn
    };
  },
  {
    persist: {
      key: "erpsparts-user",
    },
  }
);
