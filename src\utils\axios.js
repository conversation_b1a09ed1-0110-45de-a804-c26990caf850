import axios from "axios";
import { showNotify, showToast } from "vant";
import { BASE_URL } from "@/config/config";
import router from "../router";
import { useUserStore } from "@/stores/user.js";
import { isAuthCookieValid, clearAuthCookies } from "./cookie.js";

// 创建axios实例
axios.defaults.withCredentials = true;
const request = axios.create({
  baseURL: BASE_URL,
  timeout: 30000,
  withCredentials: true,
});

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 设置请求头
    config.headers = {
      ...config.headers,
      Accept: "*/*",
    };

    // 身份验证检查逻辑
    // 排除不需要身份验证的请求（登录接口等）
    const noAuthUrls = [
      'login/users/sparts', // 登录接口
      '/a/logout2',                 // 登出接口
    ];
debugger;
    const isNoAuthRequest = noAuthUrls.some(url => config.url?.includes(url));

    // 如果不是免验证请求，则检查身份验证状态
    if (!isNoAuthRequest) {
      const userStore = useUserStore();
      const hasValidCookie = isAuthCookieValid();
      const hasUserInfo = userStore.isLoggedIn();

      // 如果 cookie 无效或用户信息不存在，则清理状态并跳转登录
      if (!hasValidCookie || !hasUserInfo) {
        // 清除用户状态
        userStore.clearUserInfo();

        // 清除本地存储
        try {
          localStorage.removeItem('userInfo');
          localStorage.removeItem('erpsparts-user');
          sessionStorage.removeItem('userInfo');
        } catch (error) {
          console.warn('清除本地存储失败:', error);
        }

        // 清除身份验证 cookie
        clearAuthCookies();

        // 显示提示信息
        showToast({
          message: '登录已过期，请重新登录',
          type: 'fail',
          position: 'top'
        });

        // 跳转到登录页面
        router.replace('/login');

        // 取消当前请求
        return Promise.reject(new Error('身份验证失效，请重新登录'));
      }
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    if (response.config.responseType === "blob") {
      return response.data;
    }
    switch (response.status) {
      case 200:
        // 检查响应中是否包含登录失效信息
        if (response?.data?.includes?.("未登录或登录超时。请重新登录，谢谢")) {
          handleAuthFailure();
          return Promise.reject(new Error(`用户登录失效，请重新登录!`));
        }
        return response.data;
      case 401:
        // 处理 401 未授权状态码
        handleAuthFailure();
        return Promise.reject(new Error('身份验证失败，请重新登录'));
      case 403:
        // 处理 403 禁止访问状态码
        showNotify({ type: "danger", message: "访问被拒绝，权限不足" });
        break;
      case 500:
        showNotify({ type: "danger", message: "请求失败!" });
        break;
      default:
        console.error(`意外的状态码: ${response.status}`);
    }
  },
  (error) => {
    console.log("status-err", error);

    // 处理网络错误
    if (error.code === "ERR_NETWORK") {
      handleAuthFailure();
      return Promise.reject(error);
    }

    // 处理身份验证相关的错误状态码
    switch (error.response?.status) {
      case 401:
        handleAuthFailure();
        return Promise.reject(new Error('身份验证失败，请重新登录'));
      case 403:
        showNotify({ type: "danger", message: "访问被拒绝，权限不足" });
        break;
      case 500:
        showNotify({ type: "danger", message: "请求失败!" });
        break;
      default:
        console.error(`意外的状态码: ${error.response?.status}`);
    }
    return Promise.reject(new Error(`意外的状态码: ${error.response?.status}`));
  }
);

/**
 * 处理身份验证失败的统一逻辑
 */
function handleAuthFailure() {
  const userStore = useUserStore();

  // 清除用户状态
  userStore.clearUserInfo();

  // 清除本地存储
  try {
    localStorage.removeItem("userInfo");
    localStorage.removeItem("erpsparts-user");
    sessionStorage.removeItem("userInfo");
  } catch (error) {
    console.warn('清除本地存储失败:', error);
  }

  // 清除身份验证 cookie
  clearAuthCookies();

  // 显示提示信息
  showToast({
    message: '登录已过期，请重新登录',
    type: 'fail',
    position: 'top'
  });

  // 跳转到登录页面
  router.replace("/login");
}

/**
 * GET请求
 * @param {string} url - 请求地址
 * @param {Object} params - 请求参数
 * @param {Object} headers - 请求头
 * @param {Object} config - 额外配置
 * @returns {Promise} - 返回响应数据
 */
export const get = async (
  url,
  params,
  headers = {
    "Content-Type": "application/x-www-form-urlencoded",
  },
  config = {}
) => {
  try {
    const response = await request.get(url, { params, headers, ...config });
    return response;
  } catch (error) {
    throw error;
  }
};

/**
 * POST请求
 * @param {string} url - 请求地址
 * @param {Object} data - 请求数据
 * @param {Object} headers - 请求头
 * @param {boolean} isparams - 是否将数据转换为URLSearchParams格式
 * @returns {Promise} - 返回响应数据
 */
export const post = async (
  url,
  data,
  headers = {
    "Content-Type": "application/x-www-form-urlencoded",
  },
  isparams = true
) => {
  try {
    const formattedData = isparams ? new URLSearchParams(data).toString() : data;
    const response = await request.post(url, formattedData, { headers });
    return response;
  } catch (error) {
    throw error;
  }
};

/**
 * FormData格式的POST请求
 * @param {string} url - 请求地址
 * @param {Object} data - 请求数据
 * @returns {Promise} - 返回响应数据
 */
export const postFormData = async (url, data) => {
  try {
    const response = await request.post(url, data, {
      headers: {
        "Content-Type": undefined,
      },
    });
    return response;
  } catch (error) {
    throw error;
  }
};

// 导出请求实例，方便直接使用
export default request;
